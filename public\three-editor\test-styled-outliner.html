<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Three.js Editor - Styled Outliner Test</title>
    <meta name="viewport" content="width=device-width, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
    <link rel="stylesheet" type="text/css" href="editor/css/main.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 400px;
            margin: 0 auto;
        }
        h1 {
            text-align: center;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Styled Outliner Test</h1>
        
        <!-- Test the styled outliner panel -->
        <div class="styled-outliner-panel">
            <!-- Tab headers -->
            <div class="outliner-tabs">
                <div class="outliner-tab active">模型</div>
                <div class="outliner-tab">环境</div>
            </div>
            
            <!-- Scene objects list -->
            <div class="scene-objects-list">
                <!-- House item -->
                <div class="scene-object-item" data-object-id="1">
                    <div class="expand-btn has-children">▶</div>
                    <div class="object-icon">🏠</div>
                    <div class="object-name">House</div>
                    <div class="object-actions">
                        <div class="action-btn visibility-btn">👁</div>
                        <div class="action-btn delete-btn">🗑</div>
                    </div>
                </div>
                
                <!-- Grass item -->
                <div class="scene-object-item" data-object-id="2">
                    <div class="expand-btn has-children">▶</div>
                    <div class="object-icon">🌱</div>
                    <div class="object-name">Grass</div>
                    <div class="object-actions">
                        <div class="action-btn visibility-btn">👁</div>
                        <div class="action-btn delete-btn">🗑</div>
                    </div>
                </div>
                
                <!-- Tree item (expanded) -->
                <div class="scene-object-item selected" data-object-id="3">
                    <div class="expand-btn has-children expanded">▼</div>
                    <div class="object-icon">🌳</div>
                    <div class="object-name">Tree</div>
                    <div class="object-actions">
                        <div class="action-btn visibility-btn">👁</div>
                        <div class="action-btn delete-btn">🗑</div>
                    </div>
                </div>
                
                <!-- Tree children -->
                <div class="scene-object-item" data-object-id="4" style="padding-left: 32px;">
                    <div class="expand-btn"></div>
                    <div class="object-icon">🌳</div>
                    <div class="object-name">Tree01</div>
                    <div class="object-actions">
                        <div class="action-btn visibility-btn">👁</div>
                        <div class="action-btn delete-btn">🗑</div>
                    </div>
                </div>
                
                <div class="scene-object-item" data-object-id="5" style="padding-left: 32px;">
                    <div class="expand-btn"></div>
                    <div class="object-icon">🌳</div>
                    <div class="object-name">Tree02</div>
                    <div class="object-actions">
                        <div class="action-btn visibility-btn">👁</div>
                        <div class="action-btn delete-btn">🗑</div>
                    </div>
                </div>
                
                <!-- Dog item -->
                <div class="scene-object-item" data-object-id="6">
                    <div class="expand-btn has-children">▶</div>
                    <div class="object-icon">🐕</div>
                    <div class="object-name">Dog</div>
                    <div class="object-actions">
                        <div class="action-btn visibility-btn">🚫</div>
                        <div class="action-btn delete-btn">🗑</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Add some interactivity for testing
        document.querySelectorAll('.expand-btn.has-children').forEach(btn => {
            btn.addEventListener('click', function() {
                this.classList.toggle('expanded');
                if (this.classList.contains('expanded')) {
                    this.innerHTML = '▼';
                } else {
                    this.innerHTML = '▶';
                }
            });
        });

        document.querySelectorAll('.scene-object-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.scene-object-item').forEach(i => i.classList.remove('selected'));
                this.classList.add('selected');
            });
        });

        document.querySelectorAll('.visibility-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.stopPropagation();
                if (this.innerHTML === '👁') {
                    this.innerHTML = '🚫';
                } else {
                    this.innerHTML = '👁';
                }
            });
        });

        document.querySelectorAll('.outliner-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.outliner-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>
