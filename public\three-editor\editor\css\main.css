:root {
	color-scheme: light dark;
}

[hidden] {
	display: none !important;
}

body {
	font-family: Helvetica, Arial, sans-serif;
	font-size: 14px;
	margin: 0;
	overflow: hidden;
}

hr {
	border: 0;
	border-top: 1px solid #ccc;
}

button {
	position: relative;
}

input {
	vertical-align: middle;
}

	input[type="color"]::-webkit-color-swatch-wrapper {
		padding: 0;
	}
	input[type="color"]::-webkit-color-swatch {
		border: none;
	}

textarea {
	tab-size: 4;
	white-space: pre;
	word-wrap: normal;
}

	textarea.success {
		border-color: #8b8 !important;
	}

	textarea.fail {
		border-color: #f00 !important;
		background-color: rgba(255,0,0,0.05);
	}

textarea, input { outline: none; } /* osx */

.Panel {
	-moz-user-select: none;
	-webkit-user-select: none;
	-ms-user-select: none;

	/* No support for these yet */
	-o-user-select: none;
	user-select: none;
}

.TabbedPanel {
	-moz-user-select: none;
	-webkit-user-select: none;
	-ms-user-select: none;

	/* No support for these yet */
	-o-user-select: none;
	user-select: none;
	position: relative;
	display: block;
	width: 100%;
	min-width: 335px;
}

.TabbedPanel .Tabs {
	position: relative;
	z-index: 1; /** Above .Panels **/
	display: block;
	width: 100%;
	white-space: pre;
	overflow: hidden;
	overflow-x: auto;
}

	.TabbedPanel .Tabs::-webkit-scrollbar {
		height: 5px;
		background: #eee;
	}
	.TabbedPanel .Tabs::-webkit-scrollbar-thumb {
		background: #08f3;
	}
	.TabbedPanel .Tabs:hover::-webkit-scrollbar-thumb {
		background: #08f;
		cursor: ew-resize;
	}

	.TabbedPanel .Tabs .Tab {
		padding: 10px 9px;
		text-transform: uppercase;
	}

	.TabbedPanel .Panels {
		position: absolute;
		top: 40px;
		display: block;
		width: 100%;
	}

/* Listbox */
.Listbox {
	color: #444;
	background-color: #fff;
	padding: 0;
	width: 100%;
	min-height: 180px;
	font-size: 12px;
	cursor: default;
	overflow: auto;
}

.Listbox .ListboxItem {
	padding: 6px;
	color: #666;
	white-space: nowrap;
}

.Listbox .ListboxItem.active {
	background-color: rgba(0, 0, 0, 0.04);
}

/* CodeMirror */

.CodeMirror {

	position: absolute !important;
	top: 37px;
	width: 100% !important;
	height: calc(100% - 37px) !important;

}

	.CodeMirror .errorLine {

		background: rgba(255,0,0,0.25);

	}

	.CodeMirror .esprima-error {

		color: #f00;
		text-align: right;
		padding: 0 20px;

	}

/* outliner */

#outliner .opener {
	display: inline-block;
	width: 14px;
	height: 14px;
	margin: 0px 4px;
	vertical-align: top;
	text-align: center;
}

	#outliner .opener.open:after {
		content: '−';
	}

	#outliner .opener.closed:after {
		content: '+';
	}

#outliner .option {

	border: 1px solid transparent;

}

#outliner .option.drag {

	border: 1px dashed #999;

}

#outliner .option.dragTop {

	border-top: 1px dashed #999;

}

#outliner .option.dragBottom {

	border-bottom: 1px dashed #999;

}

#outliner .type {
	display: inline-block;
	width: 14px;
	height: 14px;
	color: #ddd;
	text-align: center;
}

#outliner .type:after {
	content: '●';
}

/* */

#outliner .Scene {
	color: #8888dd;
}

#outliner .Camera {
	color: #dd8888;
}

#outliner .Light {
	color: #dddd88;
}

/* */

#outliner .Object3D {
	color: #aaaaee;
}

#outliner .Mesh {
	color: #8888ee;
}

#outliner .Line {
	color: #88ee88;
}

#outliner .LineSegments {
	color: #88ee88;
}

#outliner .Points {
	color: #ee8888;
}

/* */

#outliner .Geometry {
	color: #aaeeaa;
}

#outliner .Material {
	color: #eeaaee;
}

/* */

#outliner .Script:after {
	content: '◎'
}

/*  */

button {
	color: #555;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 4px;
	margin: 2px;
	padding: 6px 12px;
	font-size: 12px;
	text-transform: uppercase;
	cursor: pointer;
	outline: none;
	transition: all 0.3s ease;
	box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

	button:hover {
		transform: translateY(-1px);
		box-shadow: 0 4px 8px rgba(0,0,0,0.2);
		background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
	}

	button.selected {
		background: linear-gradient(135deg, #08f 0%, #0af 100%);
		box-shadow: 0 0 0 2px rgba(0, 136, 255, 0.3);
	}

input, textarea {
	border: 1px solid #ddd;
	border-radius: 4px;
	padding: 6px 10px;
	color: #444;
	background: #fff;
	transition: all 0.3s ease;
	box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
}

input:focus, textarea:focus {
	border-color: #08f;
	box-shadow: 0 0 0 2px rgba(0, 136, 255, 0.2);
	outline: none;
}

input.Number {
	color: #08f!important;
	font-size: 12px;
	border: 0px;
	padding: 2px;
}

select {
	color: #666;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	border: 1px solid #ddd;
	border-radius: 4px;
	padding: 6px 10px;
	text-transform: uppercase;
	cursor: pointer;
	outline: none;
	transition: all 0.3s ease;
}

	select:hover {
		border-color: #08f;
		box-shadow: 0 2px 4px rgba(0,0,0,0.1);
	}

/* UI */

/* Left Resource Panel */
#resourcePanel {
	position: absolute;
	left: 0;
	top: 32px;
	bottom: 0;
	width: 280px;
	background: #f5f5f5;
	border-right: 1px solid #ddd;
	overflow: hidden;
	display: flex;
	flex-direction: row;
}

/* 分类按钮区域 */
#resourcePanel .category-buttons {
	width: 60px;
	background: #e8e8e8;
	border-right: 1px solid #ddd;
	display: flex;
	flex-direction: column;
	padding: 10px 0;
	gap: 8px;
	overflow-y: auto;
}

#resourcePanel .category-button {
	width: 50px;
	height: 50px;
	background: #fff;
	border: 1px solid #ddd;
	border-radius: 8px;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
	margin: 0 5px;
	position: relative;
}

#resourcePanel .category-button:hover {
	border-color: #08f;
	box-shadow: 0 2px 8px rgba(0, 136, 255, 0.2);
	transform: translateY(-1px);
}

#resourcePanel .category-button.active {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-color: #667eea;
	color: white;
	box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

#resourcePanel .category-button .category-icon {
	font-size: 18px;
	margin-bottom: 2px;
}

#resourcePanel .category-button .category-title {
	font-size: 8px;
	font-weight: 500;
	text-align: center;
	line-height: 1;
	color: #666;
}

#resourcePanel .category-button.active .category-title {
	color: white;
}

/* 详细内容区域 */
#resourcePanel .detail-content {
	flex: 1;
	background: #f9f9f9;
	padding: 10px; /* 减小内边距从15px到10px，为2列布局提供更多空间 */
	overflow-y: auto;
	display: none;
}

#resourcePanel .detail-content.active {
	display: block;
}

#resourcePanel .detail-title {
	font-size: 16px;
	font-weight: bold;
	color: #333;
	margin-bottom: 15px;
	padding-bottom: 8px;
	border-bottom: 2px solid #08f;
}

#resourcePanel .detail-grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
	gap: 12px;
}

/* 模型库样式 - 浅色主题 */
#resourcePanel .model-library-container {
	height: 100%;
	display: flex;
	flex-direction: column;
	background: #fff;
	color: #333;
	border-radius: 8px;
	overflow: hidden;
	border: 1px solid #e0e0e0;
}

#resourcePanel .library-tabs {
	display: flex;
	background: #f5f5f5;
	border-bottom: 1px solid #ddd;
}

#resourcePanel .library-tab {
	flex: 1;
	padding: 12px 16px;
	text-align: center;
	cursor: pointer;
	font-size: 14px;
	font-weight: 500;
	color: #666;
	border-bottom: 2px solid transparent;
	transition: all 0.3s ease;
}

#resourcePanel .library-tab.active {
	color: #333;
	border-bottom-color: #08f;
	background: rgba(0, 136, 255, 0.1);
}

#resourcePanel .library-tab:hover:not(.active) {
	color: #333;
	background: rgba(0, 0, 0, 0.05);
}

/* 内容容器样式 */
#resourcePanel .library-content-container {
	position: relative;
	flex: 1;
	overflow: hidden;
}

#resourcePanel .library-content {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	opacity: 0;
	visibility: hidden;
	transition: all 0.3s ease;
	overflow-y: auto;
}

#resourcePanel .library-content.active {
	opacity: 1;
	visibility: visible;
}

/* 空状态样式 */
#resourcePanel .library-empty-message {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 40px 20px;
	text-align: center;
	color: #999;
	min-height: 200px;
}

#resourcePanel .library-empty-message .empty-icon {
	font-size: 48px;
	margin-bottom: 16px;
	opacity: 0.6;
}

#resourcePanel .library-empty-message .empty-text {
	font-size: 16px;
	font-weight: 500;
	margin-bottom: 8px;
	color: #666;
}

#resourcePanel .library-empty-message .empty-hint {
	font-size: 14px;
	color: #999;
}

/* 多层文件夹样式 */
#resourcePanel .library-folder {
	margin-bottom: 8px;
	border: 1px solid #e0e0e0;
	border-radius: 6px;
	overflow: hidden;
}

#resourcePanel .library-folder.level-0 {
	background: #fafafa;
}

#resourcePanel .library-folder.level-1 {
	background: #f5f5f5;
	margin-left: 12px;
}

#resourcePanel .library-folder.level-2 {
	background: #f0f0f0;
	margin-left: 24px;
}

#resourcePanel .library-folder-header {
	display: flex;
	align-items: center;
	padding: 10px 12px;
	cursor: pointer;
	background: rgba(0, 0, 0, 0.02);
	border-bottom: 1px solid #e8e8e8;
	transition: background-color 0.2s ease;
}

#resourcePanel .library-folder-header:hover {
	background: rgba(0, 0, 0, 0.05);
}

#resourcePanel .library-folder-icon {
	margin-right: 8px;
	font-size: 14px;
}

#resourcePanel .library-folder-title {
	flex: 1;
	font-size: 14px;
	font-weight: 500;
	color: #333;
}

#resourcePanel .library-folder-toggle {
	font-size: 12px;
	color: #666;
	margin-left: 8px;
}

#resourcePanel .library-folder-content {
	padding: 8px;
	background: #fff;
}

#resourcePanel .library-folder-content .library-category {
	margin-bottom: 6px;
	border: 1px solid #eee;
}

#resourcePanel .library-folder-content .library-category:last-child {
	margin-bottom: 0;
}

/* 空文件夹和空分类样式 */
#resourcePanel .folder-empty-message,
#resourcePanel .category-empty-message {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 20px;
	text-align: center;
	color: #999;
	min-height: 80px;
}

#resourcePanel .folder-empty-message .empty-icon,
#resourcePanel .category-empty-message .empty-icon {
	font-size: 24px;
	margin-bottom: 8px;
	opacity: 0.6;
}

#resourcePanel .folder-empty-message .empty-text,
#resourcePanel .category-empty-message .empty-text {
	font-size: 12px;
	color: #666;
}

/* 区分文件夹和分类的空状态 */
#resourcePanel .folder-empty-message {
	background: rgba(0, 0, 0, 0.02);
	border-radius: 4px;
	margin: 8px;
}

#resourcePanel .category-empty-message {
	background: rgba(0, 0, 0, 0.01);
	border: 1px dashed #ddd;
	border-radius: 4px;
	margin: 4px 0;
}

#resourcePanel .library-search {
	padding: 12px;
	background: #f8f8f8;
	border-bottom: 1px solid #ddd;
	display: flex;
	gap: 8px;
}

#resourcePanel .library-search-input {
	flex: 1;
	padding: 8px 12px;
	background: #fff;
	border: 1px solid #ccc;
	border-radius: 4px;
	color: #333;
	font-size: 12px;
}

#resourcePanel .library-search-input::placeholder {
	color: #999;
}

#resourcePanel .library-search-input:focus {
	outline: none;
	border-color: #08f;
	box-shadow: 0 0 0 2px rgba(0, 136, 255, 0.2);
}

#resourcePanel .library-search-btn {
	width: 32px;
	height: 32px;
	background: #08f;
	border: none;
	border-radius: 4px;
	color: white;
	cursor: pointer;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: background 0.3s ease;
}

#resourcePanel .library-search-btn:hover {
	background: #0066cc;
}

#resourcePanel .library-categories {
	flex: 1;
	overflow-y: auto;
	padding: 8px;
	background: #fafafa;
}

#resourcePanel .library-category {
	margin-bottom: 8px;
	background: #fff;
	border: 1px solid #e0e0e0;
	border-radius: 6px;
	overflow: hidden;
}

#resourcePanel .library-category-header {
	padding: 10px 12px;
	background: #f5f5f5;
	cursor: pointer;
	display: flex;
	align-items: center; /* 移除justify-content: space-between，让元素自然排列 */
	transition: background 0.3s ease;
	border-bottom: 1px solid #e0e0e0;
}

#resourcePanel .library-category-header:hover {
	background: #eeeeee;
}

#resourcePanel .library-category-icon {
	margin-right: 8px;
	font-size: 14px;
}

#resourcePanel .library-category-title {
	flex: 1; /* 让标题占据剩余空间 */
	font-size: 13px;
	font-weight: 500;
	color: #333;
}

#resourcePanel .library-category-toggle {
	font-size: 12px;
	color: #666;
	transition: transform 0.3s ease;
}

#resourcePanel .library-category-content {
	padding: 6px; /* 减小内边距从8px到6px */
	background: #fff;
	width: 100%; /* 确保内容容器占满宽度 */
	box-sizing: border-box; /* 包含内边距在宽度计算中 */
}

#resourcePanel .library-models-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 3px; /* 进一步减小间距到3px */
	width: 100%; /* 确保网格占满容器宽度 */
	max-width: 100%; /* 防止溢出 */
}

#resourcePanel .library-model-item {
	background: #fff;
	border: 1px solid #ddd;
	border-radius: 3px; /* 进一步减小圆角到3px */
	padding: 3px; /* 进一步减小内边距到3px */
	cursor: grab;
	transition: all 0.3s ease;
	text-align: center;
	min-width: 0; /* 允许项目收缩 */
	width: 100%; /* 确保项目占满网格单元 */
	box-sizing: border-box; /* 包含边框和内边距在宽度计算中 */
	aspect-ratio: 1; /* 保持正方形比例 */
}

#resourcePanel .library-model-item:hover {
	border-color: #08f;
	box-shadow: 0 2px 8px rgba(0, 136, 255, 0.3);
	transform: translateY(-1px);
}

#resourcePanel .library-model-item:active {
	cursor: grabbing;
}

#resourcePanel .library-model-image {
	width: 100%;
	height: 45px; /* 进一步减小高度到45px */
	background: #f8f8f8;
	border-radius: 2px; /* 进一步减小圆角到2px */
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 18px; /* 进一步减小字体到18px */
	margin-bottom: 2px; /* 进一步减小下边距到2px */
	border: 1px solid #e0e0e0;
}

#resourcePanel .library-model-name {
	font-size: 9px; /* 进一步减小字体到9px */
	color: #555;
	font-weight: 500;
	line-height: 1.0; /* 进一步减小行高到1.0 */
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap; /* 添加文本截断 */
	padding: 0 1px; /* 添加少量水平内边距 */
}

/* 保存模型按钮样式 */
.save-model-button {
	background: #08f !important;
	color: #fff !important;
	border: none !important;
	border-radius: 4px !important;
	padding: 4px 12px !important;
	font-size: 12px !important;
	font-weight: 500 !important;
	cursor: pointer !important;
	transition: all 0.3s ease !important;
	margin-left: 10px !important;
}

.save-model-button:hover {
	background: #06d !important;
	transform: translateY(-1px) !important;
	box-shadow: 0 2px 8px rgba(0, 136, 255, 0.3) !important;
}

.save-model-button:active {
	transform: translateY(0) !important;
	box-shadow: 0 1px 4px rgba(0, 136, 255, 0.3) !important;
}

#resourcePanel .resource-item {
	width: 70px;
	height: 70px;
	background: #fff;
	border: 1px solid #ddd;
	border-radius: 8px;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	cursor: grab;
	transition: all 0.3s ease;
	position: relative;
	box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#resourcePanel .resource-item:hover {
	border-color: #08f;
	box-shadow: 0 4px 12px rgba(0, 136, 255, 0.3);
	transform: translateY(-2px);
}

#resourcePanel .resource-item:active {
	cursor: grabbing;
}

#resourcePanel .resource-item.dragging {
	opacity: 0.6;
	transform: scale(0.95) rotate(5deg);
}

#resourcePanel .resource-item .icon {
	font-size: 24px;
	margin-bottom: 4px;
	opacity: 0.8;
	transition: all 0.3s ease;
}

#resourcePanel .resource-item:hover .icon {
	opacity: 1;
	transform: scale(1.1);
}

#resourcePanel .resource-item .label {
	font-size: 10px;
	color: #666;
	text-align: center;
	line-height: 1.1;
	font-weight: 500;
}

/* 上传按钮特殊样式 */
#resourcePanel .resource-item[data-type="upload"] {
	background: linear-gradient(135deg, #4CAF50, #45a049);
	border-color: #4CAF50;
	color: white;
	cursor: pointer;
}

#resourcePanel .resource-item[data-type="upload"]:hover {
	background: linear-gradient(135deg, #45a049, #3d8b40);
	border-color: #45a049;
	box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

#resourcePanel .resource-item[data-type="upload"] .icon {
	opacity: 1;
	color: white;
}

#resourcePanel .resource-item[data-type="upload"] .label {
	color: white;
	font-weight: 600;
}

#resourcePanel .toggle-btn {
	position: absolute;
	top: 50%;
	right: 0px;
	width: 24px;
	height: 40px;
	background: linear-gradient(135deg, #e8e8e8 0%, #d0d0d0 100%);
	border: 1px solid #ddd;
	border-left: none;
	border-radius: 0px 8px 8px 0px;
	cursor: pointer;
	display: flex;
	align-items: center;
	justify-content: center;
	transform: translateY(-50%);
	z-index: 3;
	font-size: 12px;
	color: #666;
	transition: all 0.3s ease;
}

#resourcePanel .toggle-btn:hover {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

#resourcePanel.collapsed {
	width: 0px; /* 完全收缩，不占用空间 */
	border-right: none;
	overflow: visible; /* 确保切换按钮可见 */
	background: transparent; /* 透明背景 */
}

#resourcePanel.collapsed .category-buttons,
#resourcePanel.collapsed .detail-content {
	display: none; /* 隐藏面板内容 */
}

#resourcePanel.collapsed .toggle-btn {
	position: fixed; /* 使用固定定位 */
	left: 0px; /* 贴左边 */
	top: 50%; /* 垂直居中 */
	right: auto; /* 取消右侧定位 */
	transform: translateY(-50%); /* 垂直居中 */
	width: 32px;
	height: 60px;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	box-shadow: 0 0 16px rgba(0,0,0,0.5);
	border-radius: 0px 12px 12px 0px; /* 右侧圆角 */
	font-size: 16px;
	font-weight: bold;
	color: white;
	z-index: 1000; /* 确保在最上层 */
	border: 2px solid rgba(255,255,255,0.2);
}

#resourcePanel.collapsed .toggle-btn:hover {
	background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
	box-shadow: 0 0 20px rgba(0,0,0,0.6);
	transform: translateY(-50%) translateX(2px);
}

#resizer {
	position: absolute;
	z-index: 2; /* Above #sidebar */
	top: 32px;
	right: 350px;
	width: 5px;
	bottom: 0px;
	transform: translatex(2.5px);
	cursor: col-resize;
}

	#resizer:hover {
		background-color: #08f8;
		transition-property: background-color;
		transition-delay: 0.1s;
		transition-duration: 0.2s;
	}

	#resizer:active {
		background-color: #08f;
	}

#viewport {
	position: absolute;
	top: 32px;
	left: 280px;
	right: 350px;
	bottom: 0;
}

	#viewport .Text {
		text-shadow: 1px 1px 0 rgba(0,0,0,0.25);
		pointer-events: none;
	}

#script {
	position: absolute;
	top: 32px;
	left: 580px;
	right: 350px;
	bottom: 0;
	opacity: 0.9;
}

#player {
	position: absolute;
	top: 32px;
	left: 280px;
	right: 350px;
	bottom: 0;
}

#menubar {
	position: absolute;
	width: 100%;
	height: 32px;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	padding: 0;
	margin: 0;
	right: 0;
	top: 0;
	border-bottom: 1px solid #ddd;
	box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

	#menubar .menu {
		float: left;
		cursor: pointer;
		padding-right: 8px;
	}

	#menubar .menu.right {
		float: right;
		cursor: auto;
		padding-right: 0;
		text-align: right;
	}

		#menubar .menu .title {
			display: inline-block;
			color: #888;
			margin: 0;
			padding: 8px;
			line-height: 16px;
		}

		#menubar .menu .key {
			position: absolute;
			right: 10px;
			color: #ccc;
			border: 1px solid #ccc;
			border-radius: 4px;
			font-size: 9px;
			padding: 2px 4px;
			right: 10px;
			pointer-events: none;
		}

		#menubar .menu .options {
			position: fixed;
			z-index: 1; /* higher than resizer */
			display: none;
			padding: 5px 0;
			background: #eee;
			min-width: 150px;
			max-height: calc(100vh - 80px);
			overflow: auto;
		}

		#menubar .menu:hover .options {
			display: block;
			box-shadow: 0 10px 10px -5px #00000033;
		}

			#menubar .menu .options hr {
				border-color: #ddd;
			}

			#menubar .menu .options .option {
				color: #666;
				background-color: transparent;
				padding: 5px 10px;
				margin: 0 !important;
			}

				#menubar .menu .options .option:hover {
					color: #fff;
					background-color: #08f;
				}

				#menubar .menu .options .option:not(.submenu-title):active {
					color: #666;
					background: transparent;
				}

				#menubar .menu .options .option.toggle::before {

					content: ' ';
					display: inline-block;
					width: 16px;

				}

				#menubar .menu .options .option.toggle-on::before {

					content: '✔';
					font-size: 12px;

				}

				#menubar .submenu-title::after {
					content: '⏵';
					float: right;
				}

		#menubar .menu .options .inactive {
			color: #bbb;
			background-color: transparent;
			padding: 5px 10px;
			margin: 0 !important;
			cursor: not-allowed;
		}



#sidebar {
	position: absolute;
	right: 0;
	top: 32px;
	bottom: 0;
	width: 350px;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	overflow: auto;
	overflow-x: hidden;
	border-left: 1px solid #ddd;
	box-shadow: -2px 0 4px rgba(0,0,0,0.1);
	transition: right 0.3s ease;
}

/* 动画面板样式 */
#animationPanel {
	position: absolute;
	left: 300px;
	top: 32px;
	bottom: 0;
	width: 280px;
	background: linear-gradient(135deg, #e8f4f8 0%, #d1ecf1 100%);
	overflow: auto;
	overflow-x: hidden;
	border-right: 1px solid #b8dadf;
	box-shadow: 2px 0 4px rgba(0,0,0,0.1);
	transition: left 0.3s ease;
	z-index: 3;
}

	#animationPanel.collapsed {
		left: -260px;
	}

	#animationPanel .panel-header {
		background: linear-gradient(135deg, #4a90a4 0%, #357a8a 100%);
		color: white;
		padding: 12px 15px;
		font-weight: 600;
		font-size: 14px;
		text-transform: uppercase;
		letter-spacing: 0.5px;
		border-bottom: 1px solid #2c5f6f;
		box-shadow: 0 2px 4px rgba(0,0,0,0.1);
	}

	#animationPanel .panel-content {
		padding: 15px;
	}

	#animationPanel .toggle-btn {
		position: absolute;
		right: -20px;
		top: 50%;
		transform: translateY(-50%);
		width: 20px;
		height: 40px;
		background: linear-gradient(135deg, #4a90a4 0%, #357a8a 100%);
		color: white;
		border: none;
		border-radius: 4px 0 0 4px;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 12px;
		transition: all 0.2s ease;
		z-index: 4;
	}

	#animationPanel .toggle-btn:hover {
		background: linear-gradient(135deg, #357a8a 0%, #2c5f6f 100%);
		transform: translateY(-50%) translateX(2px);
	}

	/* 全局控制区域 */
	#animationPanel .global-controls {
		background: rgba(255, 255, 255, 0.7);
		border: 1px solid #b8dadf;
		border-radius: 6px;
		padding: 12px;
		margin-bottom: 15px;
	}

	#animationPanel .global-play-btn,
	#animationPanel .global-stop-btn {
		width: 48%;
		margin: 2px 1%;
		padding: 8px 12px;
		border: none;
		border-radius: 4px;
		font-size: 12px;
		font-weight: 500;
		cursor: pointer;
		transition: all 0.2s ease;
	}

	#animationPanel .global-play-btn {
		background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
		color: white;
	}

	#animationPanel .global-play-btn:hover {
		background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
		transform: translateY(-1px);
	}

	#animationPanel .global-stop-btn {
		background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
		color: white;
	}

	#animationPanel .global-stop-btn:hover {
		background: linear-gradient(135deg, #d32f2f 0%, #b71c1c 100%);
		transform: translateY(-1px);
	}

	#animationPanel .time-scale-row {
		display: flex;
		align-items: center;
		margin-top: 10px;
		gap: 8px;
	}

	#animationPanel .time-scale-label {
		font-size: 12px;
		font-weight: 500;
		color: #4a90a4;
		min-width: 40px;
	}

	#animationPanel .time-scale-input {
		flex: 1;
		padding: 4px 8px;
		border: 1px solid #b8dadf;
		border-radius: 3px;
		font-size: 12px;
	}

	/* 批量控制区域 */
	#animationPanel .batch-controls {
		margin-top: 10px;
		padding-top: 10px;
		border-top: 1px solid #b8dadf;
		display: flex;
		gap: 4px;
	}

	#animationPanel .select-all-btn,
	#animationPanel .play-selected-btn,
	#animationPanel .stop-selected-btn {
		flex: 1;
		padding: 6px 8px;
		border: none;
		border-radius: 3px;
		font-size: 11px;
		font-weight: 500;
		cursor: pointer;
		transition: all 0.2s ease;
	}

	#animationPanel .select-all-btn {
		background: linear-gradient(135deg, #607D8B 0%, #455A64 100%);
		color: white;
	}

	#animationPanel .select-all-btn:hover {
		background: linear-gradient(135deg, #455A64 0%, #37474F 100%);
	}

	#animationPanel .play-selected-btn {
		background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
		color: white;
	}

	#animationPanel .play-selected-btn:hover:not(.disabled) {
		background: linear-gradient(135deg, #388E3C 0%, #2E7D32 100%);
	}

	#animationPanel .stop-selected-btn {
		background: linear-gradient(135deg, #FF5722 0%, #D84315 100%);
		color: white;
	}

	#animationPanel .stop-selected-btn:hover:not(.disabled) {
		background: linear-gradient(135deg, #D84315 0%, #BF360C 100%);
	}

	#animationPanel .play-selected-btn.disabled,
	#animationPanel .stop-selected-btn.disabled {
		opacity: 0.5;
		cursor: not-allowed;
	}

	/* 批量控制区域 */
	#animationPanel .batch-controls {
		margin-top: 10px;
		padding-top: 10px;
		border-top: 1px solid #b8dadf;
		display: flex;
		gap: 4px;
	}

	#animationPanel .select-all-btn,
	#animationPanel .play-selected-btn,
	#animationPanel .stop-selected-btn {
		flex: 1;
		padding: 6px 8px;
		border: none;
		border-radius: 3px;
		font-size: 11px;
		font-weight: 500;
		cursor: pointer;
		transition: all 0.2s ease;
	}

	#animationPanel .select-all-btn {
		background: linear-gradient(135deg, #607D8B 0%, #455A64 100%);
		color: white;
	}

	#animationPanel .select-all-btn:hover {
		background: linear-gradient(135deg, #455A64 0%, #37474F 100%);
	}

	#animationPanel .play-selected-btn {
		background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
		color: white;
	}

	#animationPanel .play-selected-btn:hover:not(.disabled) {
		background: linear-gradient(135deg, #388E3C 0%, #2E7D32 100%);
	}

	#animationPanel .stop-selected-btn {
		background: linear-gradient(135deg, #FF5722 0%, #D84315 100%);
		color: white;
	}

	#animationPanel .stop-selected-btn:hover:not(.disabled) {
		background: linear-gradient(135deg, #D84315 0%, #BF360C 100%);
	}

	#animationPanel .play-selected-btn.disabled,
	#animationPanel .stop-selected-btn.disabled {
		opacity: 0.5;
		cursor: not-allowed;
	}

	/* 动画列表区域 */
	#animationPanel .animations-list {
		max-height: calc(100vh - 250px);
		overflow-y: auto;
	}

	#animationPanel .animation-item {
		background: rgba(255, 255, 255, 0.8);
		border: 1px solid #b8dadf;
		border-radius: 6px;
		padding: 10px;
		margin-bottom: 8px;
		transition: all 0.2s ease;
	}

	#animationPanel .animation-item:hover {
		background: rgba(255, 255, 255, 0.95);
		border-color: #4a90a4;
		transform: translateY(-1px);
		box-shadow: 0 2px 8px rgba(74, 144, 164, 0.2);
	}

	#animationPanel .animation-top-row {
		display: flex;
		align-items: center;
		gap: 8px;
		margin-bottom: 8px;
	}

	#animationPanel .animation-checkbox {
		flex-shrink: 0;
	}

	#animationPanel .animation-name {
		font-size: 13px;
		font-weight: 600;
		color: #2c5f6f;
		cursor: help;
		flex: 1;
	}

	#animationPanel .animation-controls {
		display: flex;
		gap: 4px;
	}

	#animationPanel .play-btn,
	#animationPanel .stop-btn,
	#animationPanel .loop-btn {
		flex: 1;
		padding: 6px 8px;
		border: none;
		border-radius: 3px;
		font-size: 11px;
		font-weight: 500;
		cursor: pointer;
		transition: all 0.2s ease;
	}

	#animationPanel .play-btn {
		background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
		color: white;
	}

	#animationPanel .play-btn:hover {
		background: linear-gradient(135deg, #1976D2 0%, #1565C0 100%);
	}

	#animationPanel .stop-btn {
		background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
		color: white;
	}

	#animationPanel .stop-btn:hover {
		background: linear-gradient(135deg, #F57C00 0%, #E65100 100%);
	}

	#animationPanel .loop-btn {
		background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
		color: white;
	}

	#animationPanel .loop-btn:hover {
		background: linear-gradient(135deg, #7B1FA2 0%, #6A1B9A 100%);
	}

	/* 高级控制区域 */
	#animationPanel .advanced-controls {
		margin-top: 8px;
		padding: 8px;
		background: rgba(255, 255, 255, 0.3);
		border-radius: 4px;
		border: 1px solid rgba(184, 218, 223, 0.5);
	}

	#animationPanel .weight-row,
	#animationPanel .advanced-controls .time-scale-row {
		display: flex;
		align-items: center;
		gap: 6px;
		margin-bottom: 6px;
	}

	#animationPanel .weight-row:last-child,
	#animationPanel .advanced-controls .time-scale-row:last-child {
		margin-bottom: 0;
	}

	#animationPanel .weight-label,
	#animationPanel .advanced-controls .time-scale-label {
		font-size: 11px;
		font-weight: 500;
		color: #4a90a4;
		min-width: 45px;
	}

	#animationPanel .weight-input,
	#animationPanel .advanced-controls .time-scale-input {
		flex: 1;
		padding: 3px 6px;
		border: 1px solid #b8dadf;
		border-radius: 2px;
		font-size: 11px;
	}

	#animationPanel .no-selection,
	#animationPanel .no-animations {
		text-align: center;
		color: #666;
		font-style: italic;
		padding: 20px;
		background: rgba(255, 255, 255, 0.5);
		border-radius: 6px;
		border: 1px dashed #b8dadf;
	}

	#sidebar .Panel {
		color: #888;
		padding: 10px;
		border-top: 1px solid #ccc;
	}

	#sidebar .Panel.collapsed {
		margin-bottom: 0;
	}

	#sidebar .Row {
		display: flex;
		align-items: center;
		min-height: 24px;
		margin-bottom: 10px;
	}

	#sidebar .Row .Label {

		width: 120px;

	}

/* Effects Panel Styles */
#sidebar .effects-panel {
	background: rgba(255, 255, 255, 0.05);
	border-radius: 6px;
	padding: 10px;
	margin: 10px 0;
	border: 1px solid rgba(0, 136, 255, 0.2);
}

#sidebar .effects-title {
	color: #08f;
	font-weight: bold;
	font-size: 14px;
	margin-bottom: 10px;
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

#sidebar .effect-section {
	margin: 15px 0;
	padding: 8px;
	background: rgba(255, 255, 255, 0.02);
	border-radius: 4px;
	border-left: 3px solid #08f;
}

#sidebar .effect-section .Label {
	font-size: 12px;
	color: #666;
	font-weight: 500;
}

#sidebar .glow-effect .Label {
	color: #4CAF50;
}

#sidebar .rotation-effect .Label {
	color: #FF9800;
}

/* Section Panel Styles */
#sidebar .section-panel {
	background: linear-gradient(135deg, rgba(0, 136, 255, 0.03), rgba(255, 255, 255, 0.03));
	border-radius: 8px;
	padding: 12px;
	margin: 10px 0;
	border: 1px solid rgba(0, 136, 255, 0.2);
	box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
}

/* Advanced Effects Panel Styles */
#sidebar .advanced-effects-panel {
	background: linear-gradient(135deg, rgba(0, 136, 255, 0.05), rgba(255, 255, 255, 0.05));
	border-radius: 8px;
	padding: 15px;
	margin: 15px 0;
	border: 1px solid rgba(0, 136, 255, 0.3);
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

#sidebar .advanced-effects-title {
	color: #0088ff;
	font-weight: bold;
	font-size: 16px;
	margin-bottom: 15px;
	text-transform: uppercase;
	letter-spacing: 1px;
	text-align: center;
	background: linear-gradient(45deg, #0088ff, #00ccff);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

#sidebar .bloom-effect {
	background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(129, 199, 132, 0.05));
	border-left: 4px solid #4CAF50;
	border-radius: 6px;
	padding: 12px;
	margin: 10px 0;
}

#sidebar .bloom-effect .Label {
	color: #2E7D32;
	font-weight: 500;
	font-size: 12px;
}

#tabs {
	background-color: #ddd;
	border-top: 1px solid #ccc;
}

	#tabs span {
		color: #aaa;
		border-right: 1px solid #ccc;
		padding: 10px;
	}

	#tabs span.selected {
		color: #888;
		background-color: #eee;
	}

#toolbar {
	position: absolute;
	left: 290px;
	top: 42px;
	width: 40px;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	text-align: center;
	border-radius: 8px;
	padding: 8px 4px;
	box-shadow: 0 2px 8px rgba(0,0,0,0.1);
	border: 1px solid #ddd;
	transition: left 0.3s ease; /* 添加平滑过渡 */
}

	#toolbar button, #toolbar input {
		height: 36px;
		width: 36px;
		margin: 2px 0;
		border-radius: 6px;
		display: flex;
		align-items: center;
		justify-content: center;
	}

		#toolbar button img {
			width: 18px;
			height: 18px;
			opacity: 0.7;
			transition: opacity 0.3s ease;
		}

		#toolbar button:hover img {
			opacity: 1;
		}

.Outliner {
	color: #444;
	background-color: #fff;
	padding: 0;
	width: 100%;
	height: 180px;
	font-size: 12px;
	cursor: default;
	overflow: auto;
	resize: vertical;
	outline: none !important;
}

	.Outliner .option {
		padding: 4px;
		color: #666;
		white-space: nowrap;
	}

	.Outliner .option:hover {
		background-color: rgba(0,0,0,0.02);
	}

	.Outliner .option.active {
		background-color: rgba(0,0,0,0.04);
	}


.TabbedPanel .Tabs {
	background-color: #ddd;
	border-top: 1px solid #ccc;
}

	.TabbedPanel .Tab {
		color: #aaa;
		border-right: 1px solid #ccc;
	}

	.TabbedPanel .Tab.selected {
		color: #888;
		background-color: #eee;
	}

.Listbox {
	color: #444;
	background-color: #fff;
}

.Panel {
	color: #888;
}

/* */

@media all and ( max-width: 600px ) {

	#resizer {
		display: none;
	}

	#menubar .menu .options {
		max-height: calc(100% - 80px);
	}

	#menubar .menu.right {
		display: none;
	}

	#resourcePanel {
		display: none;
	}

	#toolbar {
		left: 10px;
	}

	#viewport {
		left: 0;
		right: 0;
		top: 32px;
		height: calc(100% - 352px);
	}

	#script {
		left: 0;
		right: 0;
		top: 32px;
		height: calc(100% - 352px);
	}

	#player {
		left: 0;
		right: 0;
		top: 32px;
		height: calc(100% - 352px);
	}

	#sidebar {
		left: 0;
		width: 100%;
		top: calc(100% - 320px);
		bottom: 0;
	}

}

/* DARK MODE */

@media ( prefers-color-scheme: dark ) {

	button {
		background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
		color: #ecf0f1;
		border: 1px solid #34495e;
	}

		button:hover {
			background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
			transform: translateY(-1px);
			box-shadow: 0 4px 8px rgba(0,0,0,0.3);
		}

		button.selected {
			background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
			box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3);
		}

	input, textarea {
		background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
		border: 1px solid #34495e;
		color: #ecf0f1;
	}

	input:focus, textarea:focus {
		border-color: #3498db;
		box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
	}

	select {
		color: #ecf0f1;
		background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
		border: 1px solid #34495e;
	}

		select:hover {
			border-color: #3498db;
			box-shadow: 0 2px 4px rgba(0,0,0,0.2);
		}

	/* UI */

	#menubar {
		background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
		border-bottom-color: #34495e;
	}

			#menubar .menu .key {
				color: #444;
				border-color: #444;
			}

			#menubar .menu .options {
				background: #111;
			}

				#menubar .menu .options hr {
					border-color: #222;
				}

				#menubar .menu .options .option {
					color: #888;
				}

			#menubar .menu .options .inactive {
				color: #444;
			}

	#sidebar {
		background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
		border-left-color: #34495e;
	}

	/* 动画面板暗色主题 */
	#animationPanel {
		background: linear-gradient(135deg, #1e2a3a 0%, #2c3e50 100%);
		border-left-color: #34495e;
	}

		#animationPanel .panel-header {
			background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
			border-bottom-color: #1a252f;
		}

		#animationPanel .toggle-btn {
			background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
		}

		#animationPanel .toggle-btn:hover {
			background: linear-gradient(135deg, #2c3e50 0%, #1a252f 100%);
		}

		#animationPanel .global-controls {
			background: rgba(52, 73, 94, 0.3);
			border-color: #34495e;
		}

		#animationPanel .time-scale-label {
			color: #bdc3c7;
		}

		#animationPanel .time-scale-input {
			background: #34495e;
			border-color: #2c3e50;
			color: #ecf0f1;
		}

		#animationPanel .animation-item {
			background: rgba(52, 73, 94, 0.4);
			border-color: #34495e;
		}

		#animationPanel .animation-item:hover {
			background: rgba(52, 73, 94, 0.6);
			border-color: #5d6d7e;
		}

		#animationPanel .animation-name {
			color: #bdc3c7;
		}

		#animationPanel .no-selection,
		#animationPanel .no-animations {
			background: rgba(52, 73, 94, 0.2);
			border-color: #34495e;
			color: #95a5a6;
		}

		#animationPanel .batch-controls {
			border-top-color: #34495e;
		}

		#animationPanel .advanced-controls {
			background: rgba(52, 73, 94, 0.2);
			border-color: rgba(52, 73, 94, 0.5);
		}

		#animationPanel .weight-label,
		#animationPanel .advanced-controls .time-scale-label {
			color: #bdc3c7;
		}

		#animationPanel .weight-input,
		#animationPanel .advanced-controls .time-scale-input {
			background: #34495e;
			border-color: #2c3e50;
			color: #ecf0f1;
		}

	/* Effects Panel Dark Theme */
	#sidebar .effects-panel {
		background: rgba(52, 73, 94, 0.1);
		border-color: rgba(52, 152, 219, 0.3);
	}

	#sidebar .effects-title {
		color: #3498db;
	}

	#sidebar .effect-section {
		background: rgba(52, 73, 94, 0.05);
		border-left-color: #3498db;
	}

	#sidebar .effect-section .Label {
		color: #bdc3c7;
	}

	#sidebar .glow-effect .Label {
		color: #2ecc71;
	}

	#sidebar .rotation-effect .Label {
		color: #f39c12;
	}

	/* Section Panel Dark Theme */
	#sidebar .section-panel {
		background: linear-gradient(135deg, rgba(52, 152, 219, 0.06), rgba(52, 73, 94, 0.06));
		border-color: rgba(52, 152, 219, 0.25);
		box-shadow: 0 2px 6px rgba(0, 0, 0, 0.25);
	}

	/* Advanced Effects Panel Dark Theme */
	#sidebar .advanced-effects-panel {
		background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(52, 73, 94, 0.1));
		border-color: rgba(52, 152, 219, 0.4);
		box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
	}

	#sidebar .advanced-effects-title {
		background: linear-gradient(45deg, #3498db, #5dade2);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		background-clip: text;
	}

	#sidebar .bloom-effect {
		background: linear-gradient(135deg, rgba(46, 125, 50, 0.15), rgba(76, 175, 80, 0.08));
		border-left-color: #2ecc71;
	}

	#sidebar .bloom-effect .Label {
		color: #58d68d;
	}

/* Spotlight Panel Styles */
#sidebar #spotlight {
	background: rgba(255, 255, 255, 0.02);
	border-radius: 6px;
	padding: 10px;
}

#sidebar #spotlight .spotlight-list {
	background: rgba(0, 0, 0, 0.1);
	border-radius: 4px;
	max-height: 400px;
	overflow-y: auto;
}

#sidebar #spotlight .spotlight-list .Row {
	background: rgba(255, 255, 255, 0.05);
	border: 1px solid rgba(255, 255, 255, 0.1);
	border-radius: 4px;
	margin-bottom: 5px;
	padding: 8px;
	transition: background-color 0.2s ease;
}

#sidebar #spotlight .spotlight-list .Row:hover {
	background: rgba(255, 255, 255, 0.1);
}

#sidebar #spotlight .spotlight-list .Text {
	color: #bdc3c7;
}

#sidebar #spotlight .spotlight-list .Button {
	background: #3498db;
	color: white;
	border: none;
	border-radius: 3px;
	padding: 4px 8px;
	font-size: 11px;
	cursor: pointer;
	transition: background-color 0.2s ease;
}

#sidebar #spotlight .spotlight-list .Button:hover {
	background: #2980b9;
}

/* Spotlight Library Styles in ResourcePanel */
.spotlight-library-container {
	padding: 15px;
	height: 100%;
	overflow-y: auto;
}

.spotlight-title {
	font-size: 16px;
	font-weight: bold;
	color: #333;
	margin-bottom: 15px;
	text-align: center;
	padding: 10px;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border-radius: 6px;
}

.spotlight-filters {
	display: flex;
	gap: 10px;
	margin-bottom: 15px;
	flex-wrap: wrap;
}

.spotlight-search {
	flex: 1;
	min-width: 150px;
}

.spotlight-search-input {
	width: 100%;
	padding: 8px 12px;
	border: 1px solid #ddd;
	border-radius: 4px;
	font-size: 12px;
}

.spotlight-type-select {
	padding: 8px 12px;
	border: 1px solid #ddd;
	border-radius: 4px;
	font-size: 12px;
	background: white;
	min-width: 120px;
}

.spotlight-status {
	font-size: 12px;
	color: #666;
	margin-bottom: 10px;
	text-align: center;
	padding: 5px;
	background: rgba(0, 0, 0, 0.05);
	border-radius: 3px;
}

.spotlight-list {
	max-height: 400px;
	overflow-y: auto;
	border: 1px solid #ddd;
	border-radius: 4px;
	background: white;
}

.spotlight-item {
	display: flex;
	align-items: center;
	padding: 10px;
	border-bottom: 1px solid #eee;
	transition: background-color 0.2s ease;
}

.spotlight-item:hover {
	background-color: #f8f9fa;
}

.spotlight-item:last-child {
	border-bottom: none;
}

.spotlight-name {
	flex: 1;
	font-weight: 500;
	color: #333;
	font-size: 12px;
	margin-right: 10px;
}

.spotlight-type {
	width: 60px;
	font-size: 11px;
	color: #666;
	text-align: center;
	background: rgba(0, 0, 0, 0.05);
	padding: 2px 6px;
	border-radius: 3px;
	margin-right: 10px;
}

.spotlight-add-btn {
	background: #667eea;
	color: white;
	border: none;
	border-radius: 3px;
	padding: 6px 12px;
	font-size: 11px;
	cursor: pointer;
	transition: background-color 0.2s ease;
}

.spotlight-add-btn:hover {
	background: #5a6fd8;
}

.spotlight-empty-message {
	text-align: center;
	padding: 40px 20px;
	color: #999;
}

.spotlight-empty-message .empty-icon {
	font-size: 48px;
	margin-bottom: 15px;
}

.spotlight-empty-message .empty-text {
	font-size: 16px;
	font-weight: 500;
	margin-bottom: 8px;
}

.spotlight-empty-message .empty-hint {
	font-size: 12px;
	color: #bbb;
}

		#sidebar .Panel {
			border-top: 1px solid #222;
		}

		#sidebar .Panel.Material canvas {
			border: solid 1px #5A5A5A;
		}

	#tabs {
		background-color: #1b1b1b;
		border-top: 1px solid #222;
	}

		#tabs span {
			color: #555;
			border-right: 1px solid #222;
		}

		#tabs span.selected {
			background-color: #111;
		}

	#toolbar {
		background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
		border-color: #34495e;
	}

		#toolbar img {
			filter: invert(1);
		}

	.Outliner {
		background: #222;
	}

		.Outliner .option {
			color: #999;
		}

		.Outliner .option:hover {
			background-color: rgba(21,60,94,0.5);
		}

		.Outliner .option.active {
			background-color: rgba(21,60,94,1);
		}

	.TabbedPanel .Tabs {
		background-color: #1b1b1b;
		border-top: 1px solid #222;
	}

		.TabbedPanel .Tabs::-webkit-scrollbar {
			background: #111;
		}

		.TabbedPanel .Tab {
			color: #555;
			border-right: 1px solid #222;
		}

		.TabbedPanel .Tab.selected {
			color: #888;
			background-color: #111;
		}

	.Listbox {
		color: #888;
		background: #222;
	}

	.Listbox .ListboxItem:hover {
		background-color: rgba(21,60,94,0.5);
	}

	.Listbox .ListboxItem.active {
		background-color: rgba(21,60,94,1);
	}

	/* Dark mode for Resource Panel */
	#resourcePanel {
		background: #1a1a1a;
		border-right-color: #333;
	}

	#resourcePanel .category-buttons {
		background: #111;
		border-right-color: #333;
	}

	#resourcePanel .category-button {
		background: #222;
		border-color: #444;
	}

	#resourcePanel .category-button:hover {
		border-color: #08f;
		box-shadow: 0 2px 8px rgba(0, 136, 255, 0.4);
	}

	#resourcePanel .category-button.active {
		background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
		border-color: #5a67d8;
	}

	#resourcePanel .category-button .category-title {
		color: #aaa;
	}

	#resourcePanel .category-button.active .category-title {
		color: white;
	}

	#resourcePanel .detail-content {
		background: #1e1e1e;
	}

	#resourcePanel .detail-title {
		color: #ddd;
		border-bottom-color: #08f;
	}

	#resourcePanel .resource-item {
		background: #222;
		border-color: #444;
	}

	#resourcePanel .resource-item:hover {
		border-color: #08f;
		box-shadow: 0 4px 12px rgba(0, 136, 255, 0.4);
	}

	#resourcePanel .resource-item .label {
		color: #aaa;
	}

	/* 暗色主题下的上传按钮样式 */
	#resourcePanel .resource-item[data-type="upload"] {
		background: linear-gradient(135deg, #4CAF50, #45a049);
		border-color: #4CAF50;
	}

	#resourcePanel .resource-item[data-type="upload"]:hover {
		background: linear-gradient(135deg, #45a049, #3d8b40);
		border-color: #45a049;
		box-shadow: 0 4px 12px rgba(76, 175, 80, 0.5);
	}

	#resourcePanel .resource-item[data-type="upload"] .label {
		color: white;
	}

	#resourcePanel .toggle-btn {
		background: linear-gradient(135deg, #111 0%, #222 100%);
		border-color: #333;
		color: #aaa;
	}

	#resourcePanel .toggle-btn:hover {
		background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
		color: white;
	}

	#resourcePanel.collapsed .toggle-btn {
		background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
		box-shadow: 0 0 16px rgba(0,0,0,0.8);
		border-radius: 0px 12px 12px 0px;
		color: white;
	}

	/* 暗色主题模型库样式 */
	#resourcePanel .model-library-container {
		background: #1a1a1a;
	}

	#resourcePanel .library-tabs {
		background: #111;
		border-bottom-color: #333;
	}

	#resourcePanel .library-tab {
		color: #888;
	}

	#resourcePanel .library-tab.active {
		color: #fff;
		background: rgba(90, 103, 216, 0.2);
	}

	#resourcePanel .library-tab:hover:not(.active) {
		color: #bbb;
		background: rgba(255, 255, 255, 0.03);
	}

	/* 暗色主题空状态样式 */
	#resourcePanel .library-empty-message {
		color: #666;
	}

	#resourcePanel .library-empty-message .empty-text {
		color: #999;
	}

	#resourcePanel .library-empty-message .empty-hint {
		color: #666;
	}

	/* 暗色主题多层文件夹样式 */
	#resourcePanel .library-folder {
		border-color: #333;
	}

	#resourcePanel .library-folder.level-0 {
		background: #1a1a1a;
	}

	#resourcePanel .library-folder.level-1 {
		background: #222;
	}

	#resourcePanel .library-folder.level-2 {
		background: #2a2a2a;
	}

	#resourcePanel .library-folder-header {
		background: rgba(255, 255, 255, 0.02);
		border-bottom-color: #333;
	}

	#resourcePanel .library-folder-header:hover {
		background: rgba(255, 255, 255, 0.05);
	}

	#resourcePanel .library-folder-title {
		color: #ccc;
	}

	#resourcePanel .library-folder-toggle {
		color: #888;
	}

	#resourcePanel .library-folder-content {
		background: #111;
	}

	#resourcePanel .library-folder-content .library-category {
		border-color: #333;
	}

	/* 暗色主题空状态样式 */
	#resourcePanel .folder-empty-message,
	#resourcePanel .category-empty-message {
		color: #666;
	}

	#resourcePanel .folder-empty-message .empty-text,
	#resourcePanel .category-empty-message .empty-text {
		color: #888;
	}

	#resourcePanel .folder-empty-message {
		background: rgba(255, 255, 255, 0.02);
	}

	#resourcePanel .category-empty-message {
		background: rgba(255, 255, 255, 0.01);
		border-color: #444;
	}

	#resourcePanel .library-search {
		background: #222;
		border-bottom-color: #333;
	}

	#resourcePanel .library-search-input {
		background: #111;
		border-color: #444;
		color: #ddd;
	}

	#resourcePanel .library-search-input::placeholder {
		color: #666;
	}

	#resourcePanel .library-search-btn {
		background: #5a67d8;
	}

	#resourcePanel .library-search-btn:hover {
		background: #4c5fd6;
	}

	#resourcePanel .library-category {
		background: #222;
	}

	#resourcePanel .library-category-header {
		background: #2a2a2a;
	}

	#resourcePanel .library-category-header:hover {
		background: #333;
	}

	#resourcePanel .library-model-item {
		background: #1a1a1a;
		border-color: #333;
	}

	#resourcePanel .library-model-item:hover {
		border-color: #5a67d8;
		box-shadow: 0 2px 8px rgba(90, 103, 216, 0.4);
	}

	#resourcePanel .library-model-image {
		background: #111;
		border-color: #444;
	}

	#resourcePanel .library-model-name {
		color: #ccc;
	}

}

/* Temporary Chrome fix (#24794) */

[draggable="true"] {
	transform: translate(0, 0);
	z-index: 0;
}
